#!/usr/bin/env python3
"""
仿人散打机器人基本功能测试脚本
"""

import sys
import time

def test_imports():
    """测试导入是否正常"""
    print("=== 测试导入 ===")
    try:
        from humanoid_fighting_robot import HumanoidFightingRobot
        print("✓ 成功导入 HumanoidFightingRobot")
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_initialization():
    """测试机器人初始化"""
    print("\n=== 测试初始化 ===")
    try:
        from humanoid_fighting_robot import HumanoidFightingRobot
        robot = HumanoidFightingRobot()
        print("✓ 机器人初始化成功")
        
        # 测试基本方法是否存在
        methods = ['get_gray_values', 'get_tilt_value', 'check_balance', 
                  'navigate_by_gray', 'detect_enemy', 'attack_enemy', 'reset_arms']
        
        for method in methods:
            if hasattr(robot, method):
                print(f"✓ 方法 {method} 存在")
            else:
                print(f"✗ 方法 {method} 不存在")
        
        # 清理资源
        robot.cleanup()
        return True
        
    except Exception as e:
        print(f"✗ 初始化失败: {e}")
        return False

def test_sensor_reading():
    """测试传感器读取"""
    print("\n=== 测试传感器读取 ===")
    try:
        from humanoid_fighting_robot import HumanoidFightingRobot
        robot = HumanoidFightingRobot()
        
        # 测试灰度传感器
        left_gray, right_gray = robot.get_gray_values()
        print(f"✓ 灰度传感器读取成功 - 左: {left_gray}, 右: {right_gray}")
        
        # 测试倾角传感器
        tilt_value = robot.get_tilt_value()
        print(f"✓ 倾角传感器读取成功 - 值: {tilt_value}")
        
        # 测试敌方检测
        enemy_info = robot.detect_enemy()
        print(f"✓ 敌方检测功能正常 - 检测到: {enemy_info['detected']}")
        
        robot.cleanup()
        return True
        
    except Exception as e:
        print(f"✗ 传感器读取失败: {e}")
        return False

def test_arm_control():
    """测试胳膊控制"""
    print("\n=== 测试胳膊控制 ===")
    try:
        from humanoid_fighting_robot import HumanoidFightingRobot
        robot = HumanoidFightingRobot()
        
        print("测试重置胳膊...")
        robot.reset_arms()
        print("✓ 胳膊重置成功")
        
        robot.cleanup()
        return True
        
    except Exception as e:
        print(f"✗ 胳膊控制失败: {e}")
        return False

def main():
    """主测试函数"""
    print("仿人散打机器人基本功能测试")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_imports),
        ("初始化测试", test_initialization),
        ("传感器读取测试", test_sensor_reading),
        ("胳膊控制测试", test_arm_control)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有基本功能测试通过！")
        print("\n可以运行以下命令启动机器人:")
        print("python humanoid_fighting_robot.py")
    else:
        print("✗ 部分测试失败，请检查代码")

if __name__ == "__main__":
    main()
