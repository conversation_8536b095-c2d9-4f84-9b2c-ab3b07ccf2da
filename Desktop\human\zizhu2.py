import sys
sys.path.append("..")

from uptech import UpTech
from motion_controller import MotionController

import threading
import time
import cv2
import random
import collections
from datetime import datetime

# ====== AprilTag 检测模块导入 ======
try:
    import apriltag
    APRILTAG_AVAILABLE = True
except ImportError:
    APRILTAG_AVAILABLE = False
    print("警告: apriltag模块未安装。请运行以下命令安装:")
    print("  Linux: sudo apt-get install libapriltag-dev && pip install apriltag")
    print("  Windows: 需要安装Visual Studio和CMake，然后运行 pip install apriltag")

class HumanoidFightingRobot:
    # ========== 运动与舵机参数 ==========
    TURN_SPEED = 450      # 旋转速度（底盘左右轮速度，单位：舵机速度值）
    WALK_SPEED = 450      # 行走速度（底盘左右轮速度，单位：舵机速度值）
    ATTACK_SPEED =   500  # 攻击冲刺速度（底盘左右轮速度，单位：舵机速度值）
    TURN_TIME = 0.6       # 左右转90度所需时间（秒）
    TURN_180_TIME = 1.2   # 左右转180度所需时间（秒）

    # ========== 二维码方向定义 ==========
    QR_FRONT = 1    # 敌人正面二维码ID
    QR_LEFT = 2     # 敌人左侧二维码ID
    QR_RIGHT = 3    # 敌人右侧二维码ID
    QR_BACK = 4     # 敌人背面二维码ID

    # ========== 攻击策略参数 ==========
    ATTACK_DISTANCE = 50      # 攻击判定距离（像素，AprilTag检测用）
    SEARCH_TIME = 0.2         # 搜索敌人时旋转的时间间隔（秒）
    ENEMY_NEAR_THRESHOLD = 100  # 敌人接近判定的二维码像素阈值

    # ========== 巡逻与观察参数 ==========
    WALL_FOLLOW_SPEED = 0.7   # 沿墙巡逻时左轮速度比例（右轮为WALK_SPEED）
    CENTER_OBSERVE_INTERVAL = 0.3  # 观察中心的间隔时间（秒）
    CENTER_OBSERVE_TIME = 0.4  # 每次观察中心的持续时间（秒）

    # ========== 性能与传感器参数 ==========
    SENSOR_CACHE_SIZE = 5     # 传感器缓存队列长度
    PRINT_INTERVAL = 0.5      # 日志打印最小间隔（秒）
    CAMERA_FPS = 30           # 摄像头帧率（仅用于显示）
    TILT_CHECK_INTERVAL = 0.05  # 倾倒检测线程的检测间隔（秒）

    # ========== 倾角传感器阈值 ==========
    TILT_THRESHOLD = 250      # 后倾倒检测阈值
    TILT_SMALL_THRESHOLD = 80  # 小幅度前倾阈值
    TILT_LARGE_THRESHOLD = 600  # 大幅度前倾阈值
    NORMAL_TILT_VALUE = 2000   # 正常站立时的倾角传感器数值

    # ========== 灰度传感器阈值 ==========
    GRAY_THRESHOLD = 600      # 灰度阈值，用于检测围栏位置

    # ========== 舵机角度参数（参照rest/attack动作） ==========
    ARM_SPEED = 800           # 手臂舵机动作速度
    # rest（休息）动作角度
    ARM_REST_ANGLE_4 = 850    # 左臂肩关节
    ARM_REST_ANGLE_5 = 800    # 左臂肘关节
    ARM_REST_ANGLE_6 = 70     # 左臂腕关节
    ARM_REST_ANGLE_7 = 150    # 右臂肩关节
    ARM_REST_ANGLE_8 = 800    # 右臂肘关节
    ARM_REST_ANGLE_9 = 70     # 右臂腕关节
    # attack（攻击）动作角度
    ARM_ATTACK_ANGLE_4 = 850  # 左臂肩关节
    ARM_ATTACK_ANGLE_5 = 430  # 左臂肘关节
    ARM_ATTACK_ANGLE_6 = 410  # 左臂腕关节
    ARM_ATTACK_ANGLE_7 = 150  # 右臂肩关节
    ARM_ATTACK_ANGLE_8 = 450  # 右臂肘关节
    ARM_ATTACK_ANGLE_9 = 450  # 右臂腕关节
    # support（支撑）动作角度（用于撑起身体）
    ARM_SUPPORT_ANGLE_4 = 512  # 左臂肩关节
    ARM_SUPPORT_ANGLE_5 = 512  # 左臂肘关节
    ARM_SUPPORT_ANGLE_6 = 512  # 左臂腕关节
    ARM_SUPPORT_ANGLE_7 = 512  # 右臂肩关节
    ARM_SUPPORT_ANGLE_8 = 512  # 右臂肘关节
    ARM_SUPPORT_ANGLE_9 = 512  # 右臂腕关节

    def __init__(self):
        # ========== 硬件初始化 ==========
        self.uptech = UpTech()  # UpTech控制板对象
        self.uptech.ADC_IO_Open()  # 打开ADC和IO通道
        self.uptech.CDS_Open()     # 打开舵机通信
        time.sleep(0.3)
        
        # ========== 运动控制器初始化 ==========
        self.motion_controller = MotionController()  # 运动控制器对象
        
        # 设置手臂舵机为位置模式（0：位置模式）
        for i in [4,5,6,7,8,9]:
            self.uptech.CDS_SetMode(i, 0)
        
        # ========== 边缘检测器初始化 ==========
        # 已移除对edge_detector.py的依赖，所有功能已集成到本类中
        
        # ========== AprilTag 检测器初始化 ==========
        self.detector = None
        if APRILTAG_AVAILABLE:
            try:
                self.detector = apriltag.Detector()
                print("AprilTag检测器初始化成功")
            except Exception as e:
                print(f"AprilTag检测器初始化失败: {e}")
                self.detector = None
        else:
            print("警告: AprilTag功能不可用，机器人将无法检测敌人二维码")
        # ========== 线程与状态变量 ==========
        self.camera_activate = True  # 摄像头线程开关
        self.tilt_monitor_activate = True  # 倾倒检测线程开关
        self.is_fallen = False  # 是否检测到倾倒
        self.fall_recovery_in_progress = False  # 是否正在起身
        self.last_print_time = time.time()  # 上次日志时间
        self.sensor_cache = collections.deque(maxlen=self.SENSOR_CACHE_SIZE)  # 传感器缓存
        self.enemy_detection_count = 0  # 敌人检测计数
        self.last_enemy_detection_time = 0  # 上次检测敌人时间
        self.tag_id = None  # 当前检测到的AprilTag ID
        self.enemy_center = None  # 敌人二维码中心
        self.enemy_size = None    # 敌人二维码大小
        self.enemy_direction = None  # 敌人朝向
        self.move_cmd(0, 0)  # 启动时底盘静止

        # ========== 新增：正常站立倾角值 ==========
        self.normal_tilt_value = 2000
        
        # ========== 启动摄像头与倾倒检测线程 ==========
        try:
            self.camera_thread = threading.Thread(target=self.camera_loop)
            self.camera_thread.daemon = True
            self.camera_thread.start()
            self._log("摄像头线程已启动")
        except Exception as e:
            print(f"摄像头线程启动失败: {e}")
            raise
        try:
            self.tilt_monitor_thread = threading.Thread(target=self.tilt_monitor_loop)
            self.tilt_monitor_thread.daemon = True
            self.tilt_monitor_thread.start()
            self._log("倾倒检测线程已启动")
        except Exception as e:
            print(f"倾倒检测线程启动失败: {e}")
            raise

    def _log(self, message):
        """优化的日志输出，减少打印频率"""
        current_time = time.time()
        if current_time - self.last_print_time > self.PRINT_INTERVAL:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
            self.last_print_time = current_time

    def _get_cached_sensor_value(self, channel):
        """获取缓存的传感器值，减少重复读取"""
        current_time = time.time()
        
        # 查找缓存中的值
        for timestamp, ch, value in self.sensor_cache:
            if ch == channel and current_time - timestamp < 0.1:  # 100ms内的缓存有效
                return value
        
        # 缓存中没有或已过期，重新读取
        value = self.uptech.ADC_Get_Channel(channel)
        self.sensor_cache.append((current_time, channel, value))
        return value

    def move_cmd(self, left_speed, right_speed):
        """
        控制底盘左右轮速度。
        left_speed: 左轮速度（正为前进，负为后退）
        right_speed: 右轮速度（正为前进，负为后退）
        使用MotionController来控制移动。
        """
        self.motion_controller.move_cmd(left_speed, right_speed)

    def default_action(self):
        """
        手臂回到休息（rest）位置。
        6个舵机编号：左臂4/5/6，右臂7/8/9。
        角度参数参考fixed.py的rest动作。
        """
        self._log("手臂回到休息位置")
        self.uptech.CDS_SetAngle(4, self.ARM_REST_ANGLE_4, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(5, self.ARM_REST_ANGLE_5, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(6, self.ARM_REST_ANGLE_6, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(7, self.ARM_REST_ANGLE_7, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(8, self.ARM_REST_ANGLE_8, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(9, self.ARM_REST_ANGLE_9, self.ARM_SPEED)
        time.sleep(0.6)

    def attack_action(self):
        """
        手臂攻击（attack）动作。
        6个舵机编号：左臂4/5/6，右臂7/8/9。
        角度参数参考fixed.py的attack动作。
        """
        self._log("手臂准备攻击")
        self.uptech.CDS_SetAngle(4, self.ARM_ATTACK_ANGLE_4, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(5, self.ARM_ATTACK_ANGLE_5, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(6, self.ARM_ATTACK_ANGLE_6, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(7, self.ARM_ATTACK_ANGLE_7, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(8, self.ARM_ATTACK_ANGLE_8, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(9, self.ARM_ATTACK_ANGLE_9, self.ARM_SPEED)
        time.sleep(0.6)

    def tilt_monitor_loop(self):
        """倾倒检测线程，持续监控机器人倾倒状态"""
        self._log("倾倒检测线程开始运行")
        while self.tilt_monitor_activate:
            try:
                # 检测倾倒状态
                tilt_state = self.detect_tilt()
                
                while tilt_state > 0:  # 检测到倾倒
                    if not self.is_fallen and not self.fall_recovery_in_progress:
                        self._log(f"倾倒检测线程：检测到机器人倾倒状态 {tilt_state}！")
                        self.is_fallen = True
                        self.fall_recovery_in_progress = True
                    
                    # 反复尝试起身，直到检测到正常站立
                    while tilt_state > 0 and self.tilt_monitor_activate:
                        # 停止所有运动
                        self.move_cmd(0, 0)
                        # 根据倾倒类型执行不同的恢复动作
                        if tilt_state == 1:
                            self._log("执行后倾恢复动作")
                            self.recover_from_back_tilt()
                        elif tilt_state == 2:
                            self._log("执行小幅度前倾恢复动作")
                            self.recover_from_small_front_tilt()
                        elif tilt_state == 3:
                            self._log("执行大幅度前倾恢复动作")
                            self.recover_from_large_front_tilt()
                        # 起身动作后短暂等待再检测
                        time.sleep(0.2)
                        tilt_state = self.detect_tilt()
                        if tilt_state > 0:
                            self._log("起身失败，继续尝试...")
                    
                    if tilt_state == 0:
                        # 重置状态
                        self.is_fallen = False
                        self.fall_recovery_in_progress = False
                        self._log("倾倒检测线程：机器人已重新站立")
                
                # 检测间隔
                time.sleep(self.TILT_CHECK_INTERVAL)
                
            except Exception as e:
                self._log(f"倾倒检测线程异常: {e}")
                time.sleep(0.5)
        
        self._log("倾倒检测线程已停止")

    def camera_loop(self):
        """摄像头线程，用于AprilTag检测（持续运行）"""
        try:
            cap = cv2.VideoCapture(0)
            if not cap.isOpened():
                self._log("Failed to open camera")
                return
        except Exception as e:
            self._log(f"摄像头初始化失败: {e}")
            return

        while self.camera_activate:
            try:
                ret, frame = cap.read()
                if not ret:
                    self._log("Failed to read frame")
                    break

                if self.detector is not None:
                    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                    results = self.detector.detect(gray)

                    if results:
                        for r in results:
                            tag_id = r.tag_id
                            corners = r.corners.astype(int)
                            cv2.circle(frame, tuple(corners[0]), 4, (255, 0, 0), 2)
                            cv2.circle(frame, tuple(corners[1]), 4, (255, 0, 0), 2)
                            cv2.circle(frame, tuple(corners[2]), 4, (255, 0, 0), 2)
                            cv2.circle(frame, tuple(corners[3]), 4, (255, 0, 0), 2)
                            self.tag_id = tag_id
                            self._log(f"Detected tag_id: {tag_id}")

                            # 分析敌方机器人的方向
                            self.analyze_enemy_direction(r)

                    else:
                        self.tag_id = None
                else:
                    # 如果没有检测器，显示提示信息
                    cv2.putText(frame, "AprilTag detector not available", (10, 30), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)

                cv2.imshow("AprilTag Detection", frame)
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    self.camera_activate = False
                    break
                    
            except Exception as e:
                self._log(f"摄像头线程异常: {e}")
                time.sleep(0.1)

        cap.release()
        cv2.destroyAllWindows()
        self._log("摄像头线程已停止")

    def analyze_enemy_direction(self, detection_result):
        """
        分析敌方机器人的方向和距离
        根据检测到的二维码判断敌方机器人的朝向
        """
        tag_id = detection_result.tag_id
        center = detection_result.center
        corners = detection_result.corners

        # 计算二维码的大小（用于估算距离）
        width = abs(corners[1][0] - corners[0][0])
        height = abs(corners[2][1] - corners[1][1])
        size = (width + height) / 2

        # 估算距离（像素越小，距离越近）
        distance_estimate = "远距离" if size < 50 else "中距离" if size < 100 else "近距离"
        
        self._log(f"检测到敌方机器人二维码 {tag_id}，中心位置: {center}，大小: {size:.1f}，距离: {distance_estimate}")

        # 根据二维码ID判断敌方机器人的朝向
        if tag_id == self.QR_FRONT:
            self._log("敌方机器人正面朝向我方 - 建议绕后攻击")
            self.enemy_direction = "front_facing"
        elif tag_id == self.QR_BACK:
            self._log("敌方机器人背面朝向我方 - 最佳攻击位置，直接冲击")
            self.enemy_direction = "back_facing"
        elif tag_id == self.QR_LEFT:
            self._log("敌方机器人左侧朝向我方 - 建议向右绕后攻击")
            self.enemy_direction = "left_side"
        elif tag_id == self.QR_RIGHT:
            self._log("敌方机器人右侧朝向我方 - 建议向左绕后攻击")
            self.enemy_direction = "right_side"
        else:
            self._log(f"未知二维码ID: {tag_id} - 使用默认攻击策略")
            self.enemy_direction = "unknown"

        # 存储敌方位置信息
        self.enemy_center = center
        self.enemy_size = size
        
        # 输出攻击建议
        if size >= self.ENEMY_NEAR_THRESHOLD:
            self._log(f"敌人距离较近(size={size:.1f})，准备攻击！")
        else:
            self._log(f"敌人距离较远(size={size:.1f})，继续接近...")


    def detect_tilt(self):
        """
        检测机器人倾倒状态（三种情况）
        返回值：
        0 - 正常站立
        1 - 后倾
        2 - 前倾幅度较小
        3 - 前倾幅度较大
        """
        current_tilt = self._get_cached_sensor_value(2)  # 倾角传感器ADC2
        tilt_difference = current_tilt - self.normal_tilt_value  # 正值表示前倾，负值表示后倾

        # 减少打印频率
        if abs(tilt_difference) > self.TILT_SMALL_THRESHOLD:
            self._log(f"倾角异常: {current_tilt}, 差值: {tilt_difference}")

        # 判断倾倒类型
        if tilt_difference < -self.TILT_THRESHOLD:
            # 后倾（负值较大）
            self._log("检测到机器人后倾！")
            return 1
        elif tilt_difference > self.TILT_LARGE_THRESHOLD:
            # 大幅度前倾
            self._log("检测到机器人大幅度前倾！")
            return 3
        elif tilt_difference > self.TILT_SMALL_THRESHOLD:
            # 小幅度前倾
            self._log("检测到机器人小幅度前倾！")
            return 2
        else:
            # 正常站立
            return 0

    def support_body_with_arms(self):
        """用手臂撑起身体（当检测到倾倒时）"""
        self._log("检测到倾倒，用手臂撑起身体")

        # 停止所有移动
        self.move_cmd(0, 0)
        time.sleep(0.1)

        # 手臂伸展到支撑位置
        self.uptech.CDS_SetAngle(4, self.ARM_SUPPORT_ANGLE_4, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(5, self.ARM_SUPPORT_ANGLE_5, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(6, self.ARM_SUPPORT_ANGLE_6, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(7, self.ARM_SUPPORT_ANGLE_7, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(8, self.ARM_SUPPORT_ANGLE_8, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(9, self.ARM_SUPPORT_ANGLE_9, self.ARM_SPEED)
        time.sleep(1.0)

        # 尝试用腿部力量配合手臂重新站立
        self._log("尝试重新站立")
        self.move_cmd(-200, -200)  # 轻微后退
        time.sleep(0.1)
        self.move_cmd(200, 200)   # 轻微前进
        time.sleep(0.1)
        self.move_cmd(0, 0)

        # 手臂回到休息位置
        time.sleep(0.1)
        self.default_action()

        self._log("重新站立完成")

    def recover_from_back_tilt(self):
        """后倾恢复动作"""
        self._log("开始后倾恢复动作")
        
        # 停止所有移动
        self.move_cmd(0, 0)
        time.sleep(0.1)
        
        # 手臂伸展到支撑位置
        self.uptech.CDS_SetAngle(4, 150, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(7, 850, self.ARM_SPEED)
        time.sleep(2)
        self.uptech.CDS_SetAngle(5, 700, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(6, 430, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(8, 700, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(9, 471, self.ARM_SPEED)
        time.sleep(1)

        self.default_action()
        time.sleep(0.5)

        # 后倾恢复：向前推动身体
        self._log("向前推动身体")
        self.move_cmd(300, 300)  # 前进
        time.sleep(0.3)
        self.move_cmd(0, 0)
        time.sleep(0.2)
        
        # 手臂回到休息位置
        time.sleep(0.1)
        self.default_action()
        
        self._log("后倾恢复完成")

    def recover_from_small_front_tilt(self):
        """小幅度前倾恢复动作"""
        self._log("开始小幅度前倾恢复动作")
        
        # 停止所有移动
        self.move_cmd(0, 0)
        time.sleep(0.1)

        self.uptech.CDS_SetAngle(5, 700, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(6, 430, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(8, 700, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(9, 471, self.ARM_SPEED)
        time.sleep(1)
        
        # 手臂回到休息位置
        self.default_action()
        
        self._log("小幅度前倾恢复完成")

    def recover_from_large_front_tilt(self):
        """大幅度前倾恢复动作"""
        self._log("开始大幅度前倾恢复动作")
        
        # 停止所有移动
        self.move_cmd(0, 0)
        time.sleep(0.1)
        
        # 手臂伸展到支撑位置
        self.uptech.CDS_SetAngle(4, 471, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(7, 471, self.ARM_SPEED)
        time.sleep(1)

        self.uptech.CDS_SetAngle(4, self.ARM_REST_ANGLE_4, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(7, self.ARM_REST_ANGLE_7, self.ARM_SPEED)
        time.sleep(2)

        self.uptech.CDS_SetAngle(5, 700, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(6, 430, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(8, 700, self.ARM_SPEED)
        self.uptech.CDS_SetAngle(9, 471, self.ARM_SPEED)
        time.sleep(1)

        self.default_action()
        time.sleep(0.5)

        # 大幅度前倾：后退并用手臂支撑
        self._log("后退并用手臂支撑")
        self.move_cmd(-300, -300)  # 后退
        time.sleep(0.2)
        self.move_cmd(0, 0)
        time.sleep(0.2)
        
        # 尝试重新站立
        self.move_cmd(200, 200)  # 前进
        time.sleep(0.2)
        self.move_cmd(0, 0)
        time.sleep(0.1)
        
        # 手臂回到休息位置
        time.sleep(0.1)
        self.default_action()
        
        self._log("大幅度前倾恢复完成")

    def attack_enemy(self):
        """攻击敌方机器人（无绕后策略，按二维码编号决定推力）"""
        if not hasattr(self, 'enemy_center') or self.enemy_center is None:
            return

        self._log(f"攻击敌方机器人，方向: {self.enemy_direction}")

        # 不管敌方朝向，直接推
        if self.enemy_direction in ["front_facing", "back_facing"]:
            # 编号1或4，用力推
            self._log("敌方正面或背面，强力推进")
            self.move_cmd(self.ATTACK_SPEED, self.ATTACK_SPEED)
            time.sleep(0.5)
            self.move_cmd(0, 0)
            time.sleep(0.1)
            self.execute_rapid_attacks(attack_count=3, attack_interval=0.2)
        elif self.enemy_direction in ["left_side", "right_side"]:
            # 编号2或3，慢速推
            self._log("敌方侧面，慢速推进")
            self.move_cmd(self.WALK_SPEED, self.WALK_SPEED)
            time.sleep(0.5)
            self.move_cmd(0, 0)
            time.sleep(0.1)
            self.execute_rapid_attacks(attack_count=2, attack_interval=0.2)
        else:
            # 未知方向，默认强力推
            self._log("未知敌人方向，强力推进")
            self.move_cmd(self.ATTACK_SPEED, self.ATTACK_SPEED)
            time.sleep(0.5)
            self.move_cmd(0, 0)
            time.sleep(0.1)
            self.execute_rapid_attacks(attack_count=2, attack_interval=0.2)

        # 攻击后确保手臂回到休息位置
        self.default_action()

    def execute_single_attack(self):
        """
        执行一次完整的攻击动作（出拳-收拳）
        """
        self._log("执行攻击动作")
        # 出拳
        self.attack_action()
        time.sleep(0.2)  # 保持攻击姿态短暂时间
        
        # 收拳
        self.default_action()
        time.sleep(0.1)  # 短暂等待确保收拳完成

    def execute_rapid_attacks(self, attack_count=3, attack_interval=0.3):
        """
        执行连续快速攻击
        :param attack_count: 攻击次数
        :param attack_interval: 攻击间隔（秒）
        """
        self._log(f"执行连续攻击，次数: {attack_count}")
        for i in range(attack_count):
            self.execute_single_attack()
            if i < attack_count - 1:  # 不是最后一次攻击
                time.sleep(attack_interval - 0.2)  # 减去攻击动作本身的时间

    def circle_to_back_attack(self, enemy_side):
        """
        绕到敌人后面进行攻击（优化版）
        :param enemy_side: 敌人侧面方向 ("left_side" 或 "right_side")
        """
        self._log(f"敌人{enemy_side}，开始绕后攻击")
        
        # 根据敌人侧面方向选择绕行方向
        if enemy_side == "left_side":
            # 敌人左侧朝向我方，我向右绕到其后面
            self._log("向右绕行到敌人后面")
            self.move_cmd(self.WALK_SPEED, -self.WALK_SPEED)
            time.sleep(0.8)  # 右转约90度
        else:  # right_side
            # 敌人右侧朝向我方，我向左绕到其后面
            self._log("向左绕行到敌人后面")
            self.move_cmd(-self.WALK_SPEED, self.WALK_SPEED)
            time.sleep(0.8)  # 左转约90度
        
        self.move_cmd(0, 0)
        time.sleep(0.2)
        
        # 前进接近敌人
        self._log("前进接近敌人")
        self.move_cmd(self.ATTACK_SPEED, self.ATTACK_SPEED)
        time.sleep(0.4)
        
        self.move_cmd(0, 0)
        time.sleep(0.1)
        
        # 执行攻击
        self.execute_rapid_attacks(attack_count=2, attack_interval=0.2)

    def continuous_chase_attack(self, max_chase_time=10.0):
        """
        连续追击攻击敌人（只要能检测到敌人就持续攻击，敌人消失则原地寻找一会儿）
        :param max_chase_time: 最大追击时间（秒），防止极端情况死循环
        """
        self._log("开始连续追击攻击")
        chase_start = time.time()
        search_timeout = 2.0  # 敌人消失后原地寻找的时间（秒）
        
        while time.time() - chase_start < max_chase_time:
            # 检查是否还能看到敌人
            if hasattr(self, 'tag_id') and self.tag_id is not None:
                # 根据敌人朝向选择追击策略
                if hasattr(self, 'enemy_direction'):
                    if self.enemy_direction == "back_facing":
                        self._log("敌人背对我方，直接追击")
                        self.move_cmd(self.ATTACK_SPEED, self.ATTACK_SPEED)
                        time.sleep(0.2)
                        self.move_cmd(0, 0)
                        time.sleep(0.1)
                        self.execute_single_attack()
                    elif self.enemy_direction == "front_facing":
                        self._log("敌方正对我方，策略性追击")
                        self.move_cmd(self.WALK_SPEED, -self.WALK_SPEED)
                        time.sleep(0.2)
                        self.move_cmd(0, 0)
                        time.sleep(0.1)
                        self.execute_single_attack()
                    elif self.enemy_direction in ["left_side", "right_side"]:
                        self._log(f"敌人{self.enemy_direction}，继续追击")
                        self.move_cmd(self.WALK_SPEED, self.WALK_SPEED)
                        time.sleep(0.2)
                        self.move_cmd(0, 0)
                        time.sleep(0.1)
                        self.execute_single_attack()
                    else:
                        self.move_cmd(self.ATTACK_SPEED, self.ATTACK_SPEED)
                        time.sleep(0.2)
                        self.move_cmd(0, 0)
                        time.sleep(0.1)
                        self.execute_single_attack()
                else:
                    self.move_cmd(self.ATTACK_SPEED, self.ATTACK_SPEED)
                    time.sleep(0.2)
                    self.move_cmd(0, 0)
                    time.sleep(0.1)
                    self.execute_single_attack()
                time.sleep(0.1)
            else:
                # 敌人不见，原地寻找一会儿
                self._log("失去敌人目标，原地寻找...")
                search_start = time.time()
                found = False
                while time.time() - search_start < search_timeout:
                    # 原地缓慢旋转
                    self.move_cmd(self.TURN_SPEED//2, -self.TURN_SPEED//2)
                    time.sleep(0.3)
                    self.move_cmd(0, 0)
                    time.sleep(0.1)
                    # 检查是否重新检测到敌人
                    if hasattr(self, 'tag_id') and self.tag_id is not None:
                        self._log("重新发现敌人，继续攻击")
                        found = True
                        break
                if not found:
                    self._log("长时间未发现敌人，停止追击")
                    break
        self._log("连续追击攻击结束")



    def start(self):
        """仿人散打主控制循环（灰度引导靠边+原地旋转攻击模式）"""
        print("仿人散打机器人启动（灰度引导靠边+原地旋转攻击模式）")
        self.default_action()
        time.sleep(0.2)
        
        # 确保机器人停止
        self.move_cmd(0, 0)
        time.sleep(0.1)
        
        # 1. 向前走一段距离
        print("启动：向前行进2秒")
        self.move_cmd(self.WALK_SPEED, self.WALK_SPEED)
        time.sleep(2.0)
        self.move_cmd(0, 0)
        time.sleep(0.2)
        
        # 2. 右转90度面向擂台
        print("右转90度面向擂台")
        self.move_cmd(self.TURN_SPEED, -self.TURN_SPEED)
        time.sleep(self.TURN_TIME)
        self.move_cmd(0, 0)
        time.sleep(0.2)
        
        print("进入原地旋转+灰度引导模式")
        while True:
            try:
                # 倾倒检测（由线程管理）
                if self.is_fallen or self.fall_recovery_in_progress:
                    print("机器人倾倒中，等待恢复...")
                    while self.is_fallen or self.fall_recovery_in_progress:
                        time.sleep(0.1)
                    print("机器人已恢复，继续运行")
                    time.sleep(0.2)
                    continue

                # 灰度检测，靠近围栏
                front_gray = self._get_cached_sensor_value(0)
                back_gray = self._get_cached_sensor_value(1)
                print(f"前灰度: {front_gray}, 后灰度: {back_gray}")

                if front_gray < self.GRAY_THRESHOLD:
                    # 前方靠近围栏，后退一点
                    print("前方靠近围栏，后退")
                    self.move_cmd(-self.WALK_SPEED, -self.WALK_SPEED)
                    time.sleep(0.3)
                    self.move_cmd(self.TURN_SPEED, -self.TURN_SPEED)
                    self.sleep(self.TURN_180_TIME)
                    self.move_cmd(0, 0)
                    time.sleep(0.1)
                elif back_gray < self.GRAY_THRESHOLD:
                    # 后方靠近围栏，前进一点
                    print("后方靠近围栏，前进")
                    self.move_cmd(self.WALK_SPEED, self.WALK_SPEED)
                    time.sleep(0.2)
                    self.move_cmd(0, 0)
                    time.sleep(0.1)
                else:
                    # 不靠近围栏，原地缓慢右旋
                    print("不靠近围栏，原地缓慢右旋")
                    self.move_cmd(self.TURN_SPEED//2, -self.TURN_SPEED//2)
                    time.sleep(0.3)
                    self.move_cmd(self.WALK_SPEED, self.WALK_SPEED)
                    time.sleep(0.5)
                    self.move_cmd(0, 0)
                    time.sleep(0.1)

                # 检查AprilTag敌人
                if hasattr(self, 'enemy_size') and self.enemy_size is not None and self.enemy_size >= self.ENEMY_NEAR_THRESHOLD:
                    print("检测到敌人接近，执行攻击")
                    self.attack_enemy()
                    time.sleep(0.5)

                time.sleep(0.05)
            except Exception as e:
                print(f"主循环异常: {e}")
                self.move_cmd(0, 0)
                self.default_action()
                time.sleep(0.5)

    def cleanup(self):
        """清理资源，关闭所有线程"""
        self._log("开始清理资源...")
        
        # 停止倾倒检测线程
        self.tilt_monitor_activate = False
        if hasattr(self, 'tilt_monitor_thread') and self.tilt_monitor_thread.is_alive():
            self.tilt_monitor_thread.join(timeout=2.0)
            self._log("倾倒检测线程已停止")
        
        # 停止摄像头线程
        self.camera_activate = False
        if hasattr(self, 'camera_thread') and self.camera_thread.is_alive():
            self.camera_thread.join(timeout=2.0)
            self._log("摄像头线程已停止")
        
        # 停止所有运动
        self.move_cmd(0, 0)
        
        # 手臂回到休息位置
        self.default_action()
        
        # 安全清理AprilTag检测器
        if hasattr(self, 'detector'):
            try:
                del self.detector
            except:
                pass
        
        self._log("资源清理完成")

    def __del__(self):
        """析构函数，确保资源被正确释放"""
        try:
            if hasattr(self, 'detector'):
                # 安全删除检测器，避免AttributeError
                try:
                    del self.detector
                except:
                    pass
            self.cleanup()
        except:
            pass

def main():
    """主函数"""
    robot = None
    try:
        robot = HumanoidFightingRobot()
        robot._log("仿人散打机器人初始化完成")
        
        # 等待系统稳定
        time.sleep(0.5)

        # 简单的启动检测
        robot._log("机器人准备就绪，开始仿人散打")
        current_tilt = robot.uptech.ADC_Get_Channel(2)  # 倾角传感器ADC2
        front_gray = robot.uptech.ADC_Get_Channel(0)  # 前方灰度传感器ADC0
        back_gray = robot.uptech.ADC_Get_Channel(1)   # 后方灰度传感器ADC1
        # 测试手臂动作
        robot.default_action()
        robot.attack_action()
        robot.default_action()
        
        # 确保机器人停止
        robot.move_cmd(600, 600)
        time.sleep(2)
        robot.move_cmd(0, 0)
        time.sleep(1)
        
        robot._log(f"倾角值：{current_tilt}")
        robot._log(f"前灰度值：{front_gray}")
        robot._log(f"后灰度值：{back_gray}")
        
        # 开始运行主循环
        robot.start()
        
    except KeyboardInterrupt:
        print("\n收到中断信号，正在停止机器人...")
    except Exception as e:
        print(f"程序运行异常: {e}")
    finally:
        # 确保资源被正确清理
        if robot:
            robot.cleanup()
        print("程序已退出")

if __name__ == "__main__":
    main() 